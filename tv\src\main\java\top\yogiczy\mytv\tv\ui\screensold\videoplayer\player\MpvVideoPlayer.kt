package top.yogiczy.mytv.tv.ui.screensold.videoplayer.player

import android.content.Context
import android.graphics.SurfaceTexture
import android.view.LayoutInflater
import android.view.Surface
import android.view.SurfaceView
import android.view.TextureView
import android.view.View
import `is`.xyz.mpv.MPVLib
import `is`.xyz.mpv.MPVView
import `is`.xyz.mpv.Utils
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import top.yogiczy.mytv.core.data.entities.channel.ChannelLine
import top.yogiczy.mytv.core.util.utils.toHeaders
import top.yogiczy.mytv.tv.R
import top.yogiczy.mytv.tv.ui.utils.Configs

/**
 * MPV-based player using MPVLib and MPVView, reimplemented following IjkVideoPlayer pattern.
 */
class MpvVideoPlayer(
    private val context: Context,
    private val coroutineScope: CoroutineScope,
) : VideoPlayer(coroutineScope) {

    private var mpvView: MPVView? = null
    private var updateJob: Job? = null
    private var lastVolume: Float = 1f
    private var initialized: Boolean = false
    private var cacheSurfaceView: SurfaceView? = null
    private var cacheSurfaceTexture: Surface? = null
    private var isPrepared: Boolean = false
    private var isPlaybackStarted: Boolean = false

    override fun initialize() {
        super.initialize()
    }

    override fun release() {
        updateJob?.cancel()
        updateJob = null
        mpvView?.let { view ->
            runCatching {
                MPVLib.setPropertyString("pause", "yes")
                MPVLib.command("quit")
            }
        }
        cacheSurfaceTexture?.release()
        cacheSurfaceTexture = null
        cacheSurfaceView = null
        mpvView = null
        initialized = false
        isPrepared = false
        isPlaybackStarted = false
        super.release()
    }

    override fun prepare(line: ChannelLine) {
        runCatching {
            // Stop previous media item if configured
            if (initialized && Configs.videoPlayerStopPreviousMediaItem) {
                stop()
            }

            // Reset state
            isPrepared = false
            isPlaybackStarted = false

            val view = ensureMpvView()
            if (!initialized) {
                // Copy assets and initialize MPV
                Utils.copyAssets(context)
                view.initialize(context.filesDir.path, context.cacheDir.path)
                initialized = true
            }

            // Set MPV options similar to IjkVideoPlayer
            setMpvOptions(line)

            // Load file with replace mode to ensure clean transition
            MPVLib.command("loadfile", line.playableUrl, "replace")

            triggerPrepared()

            // Start monitoring playback state
            startUpdateLoop()

        }.onFailure { exception ->
            triggerError(PlaybackException("MPV_ERROR_PREPARE_FAILED", 20001))
        }
    }

    private fun setMpvOptions(line: ChannelLine) {
        // Basic options
        MPVLib.setOptionString("user-agent", line.httpUserAgent ?: Configs.videoPlayerUserAgent)
        MPVLib.setOptionString("rtsp-transport", "udp")

        // Network and timeout options similar to IjkVideoPlayer
        MPVLib.setOptionString("network-timeout", Configs.videoPlayerLoadTimeout.toString())
        MPVLib.setOptionString("stream-open-timeout", Configs.videoPlayerLoadTimeout.toString())

        // Cache options for better streaming performance
        // MPVLib.setOptionString("cache", "yes")
        // MPVLib.setOptionString("cache-secs", "10")
        // MPVLib.setOptionString("cache-pause-initial", "yes")
        // MPVLib.setOptionString("cache-pause-wait", "3")
        MPVLib.setOptionString("demuxer-max-bytes", "50M")
        MPVLib.setOptionString("demuxer-max-back-bytes", "25M")
        MPVLib.setOptionString("demuxer-readahead-secs", "5")

        // Hardware decoding options
        MPVLib.setOptionString("hwdec", "auto-safe")
        MPVLib.setOptionString("vo", "gpu")

        // Audio options
        MPVLib.setOptionString("audio-channels", "stereo")

        // Network retry options for better reliability
        MPVLib.setOptionString("stream-lavf-o", "reconnect=1,reconnect_at_eof=1,reconnect_streamed=1,reconnect_delay_max=5")

        // Additional headers if available
        val headers = Configs.videoPlayerHeaders.toHeaders()
        if (headers.isNotEmpty()) {
            val headerString = headers.map { "${it.key}: ${it.value}" }.joinToString("\r\n")
            MPVLib.setOptionString("http-header-fields", headerString)
        }
    }

    private fun startUpdateLoop() {
        updateJob?.cancel()
        updateJob = coroutineScope.launch {
            var hasTriggeredReady = false
            var lastWidth = 0
            var lastHeight = 0
            var errorCheckCount = 0
            val maxErrorChecks = 10 // Check for errors for up to 5 seconds

            while (true) {
                runCatching {
                    // Check for playback errors early
                    if (!hasTriggeredReady && errorCheckCount < maxErrorChecks) {
                        val eofReached = MPVLib.getPropertyBoolean("eof-reached") ?: false
                        val coreIdle = MPVLib.getPropertyBoolean("core-idle") ?: false

                        if (eofReached && coreIdle) {
                            errorCheckCount++
                            if (errorCheckCount >= maxErrorChecks) {
                                triggerError(PlaybackException("MPV_ERROR_STREAM_FAILED", 20009))
                                return@launch
                            }
                        } else {
                            errorCheckCount = 0 // Reset counter if playback seems to be working
                        }
                    }

                    // Check if file is loaded and trigger ready event once
                    if (!hasTriggeredReady) {
                        val duration = MPVLib.getPropertyDouble("duration")
                        val timePos = MPVLib.getPropertyDouble("time-pos")

                        // For live streams, duration might be null but time-pos should be available
                        if ((duration != null && duration > 0) || (timePos != null && timePos >= 0)) {
                            hasTriggeredReady = true
                            isPrepared = true

                            // Extract and trigger metadata similar to IjkVideoPlayer.onPrepared
                            extractAndTriggerMetadata()
                            triggerReady()
                            triggerBuffering(false)

                            // Set duration for live streams to a large value if not available
                            val actualDuration = duration ?: Long.MAX_VALUE.toDouble() / 1000.0
                            triggerDuration((actualDuration * 1000).toLong())
                        }
                    }

                    // Update duration and position (convert seconds to milliseconds)
                    MPVLib.getPropertyDouble("duration")?.let {
                        triggerDuration((it * 1000).toLong())
                    }
                    MPVLib.getPropertyDouble("time-pos")?.let {
                        triggerCurrentPosition((it * 1000).toLong())
                    }

                    // Update playing state
                    val isPaused = MPVLib.getPropertyBoolean("pause") ?: true
                    triggerIsPlayingChanged(!isPaused)

                    // Update resolution if changed
                    val width = MPVLib.getPropertyInt("video-params/w") ?: 0
                    val height = MPVLib.getPropertyInt("video-params/h") ?: 0
                    if (width > 0 && height > 0 && (width != lastWidth || height != lastHeight)) {
                        lastWidth = width
                        lastHeight = height
                        triggerResolution(width, height)
                    }
                }
                delay(500)
            }
        }
    }

    override fun play() {
        runCatching {
            MPVLib.setPropertyString("pause", "no")
        }.onFailure {
            triggerError(PlaybackException("MPV_ERROR_PLAY_FAILED", 20002))
        }
    }

    override fun pause() {
        runCatching {
            MPVLib.setPropertyString("pause", "yes")
        }.onFailure {
            triggerError(PlaybackException("MPV_ERROR_PAUSE_FAILED", 20003))
        }
    }

    override fun seekTo(position: Long) {
        runCatching {
            val seconds = position / 1000.0
            MPVLib.command("seek", seconds.toString(), "absolute", "exact")
        }.onFailure {
            triggerError(PlaybackException("MPV_ERROR_SEEK_FAILED", 20004))
        }
    }

    override fun setVolume(volume: Float) {
        runCatching {
            lastVolume = volume.coerceIn(0f, 1f)
            val vol100 = (lastVolume * 100).coerceIn(0f, 100f)
            MPVLib.setPropertyString("volume", vol100.toString())
        }.onFailure {
            triggerError(PlaybackException("MPV_ERROR_VOLUME_FAILED", 20005))
        }
    }

    override fun getVolume(): Float = lastVolume

    override fun stop() {
        runCatching {
            // First pause to stop playback immediately
            MPVLib.setPropertyString("pause", "yes")
            // Then stop the current file
            MPVLib.command("stop")
            // Clear the playlist to ensure clean state
            MPVLib.command("playlist-clear")
        }
        updateJob?.cancel()
        updateJob = null
        isPrepared = false
        isPlaybackStarted = false
        super.stop()
    }

    override fun selectVideoTrack(track: Metadata.Video?) {
        track?.index?.let { index ->
            runCatching {
                MPVLib.setPropertyString("vid", index.toString())
            }.onFailure {
                triggerError(PlaybackException("MPV_ERROR_VIDEO_TRACK_FAILED", 20006))
            }
        }
    }

    override fun selectAudioTrack(track: Metadata.Audio?) {
        track?.index?.let { index ->
            runCatching {
                MPVLib.setPropertyString("aid", index.toString())
            }.onFailure {
                triggerError(PlaybackException("MPV_ERROR_AUDIO_TRACK_FAILED", 20007))
            }
        }
    }

    override fun selectSubtitleTrack(track: Metadata.Subtitle?) {
        track?.index?.let { index ->
            runCatching {
                MPVLib.setPropertyString("sid", index.toString())
            }.onFailure {
                triggerError(PlaybackException("MPV_ERROR_SUBTITLE_TRACK_FAILED", 20008))
            }
        } ?: run {
            // Disable subtitles
            runCatching {
                MPVLib.setPropertyString("sid", "no")
            }
        }
    }

    override fun setVideoSurfaceView(surfaceView: SurfaceView) {
        cacheSurfaceView = surfaceView
        cacheSurfaceTexture?.release()
        cacheSurfaceTexture = null

        // MPV renders via its own view, but we cache the surface for potential future use
        if (isPrepared) {
            // If already prepared, we might need to handle surface changes
        }
    }

    override fun setVideoTextureView(textureView: TextureView) {
        cacheSurfaceView = null
        textureView.surfaceTextureListener = object : TextureView.SurfaceTextureListener {
            override fun onSurfaceTextureAvailable(
                surfaceTexture: SurfaceTexture,
                width: Int,
                height: Int
            ) {
                cacheSurfaceTexture = Surface(surfaceTexture)
                // MPV handles its own rendering, but we cache for potential use
            }

            override fun onSurfaceTextureSizeChanged(
                surfaceTexture: SurfaceTexture,
                width: Int,
                height: Int
            ) {
                // Handle size changes if needed
            }

            override fun onSurfaceTextureDestroyed(surfaceTexture: SurfaceTexture): Boolean {
                cacheSurfaceTexture?.release()
                cacheSurfaceTexture = null
                return true
            }

            override fun onSurfaceTextureUpdated(surfaceTexture: SurfaceTexture) {
                // Handle updates if needed
            }
        }
    }

    override fun buildCustomView(context: Context): View? = ensureMpvView()

    private fun ensureMpvView(): MPVView {
        mpvView?.let { return it }
        val view = LayoutInflater.from(context).inflate(R.layout.view_mpv, null, false) as MPVView
        mpvView = view
        return view
    }

    private fun extractAndTriggerMetadata() {
        runCatching {
            val metadata = Metadata(
                video = extractVideoMetadata(),
                audio = extractAudioMetadata(),
                subtitle = extractSubtitleMetadata(),
                videoTracks = extractVideoTracks(),
                audioTracks = extractAudioTracks(),
                subtitleTracks = extractSubtitleTracks()
            )
            triggerMetadata(metadata)
        }.onFailure {
            // If metadata extraction fails, continue without it
        }
    }

    private fun extractVideoMetadata(): Metadata.Video? {
        return runCatching {
            val width = MPVLib.getPropertyInt("video-params/w")
            val height = MPVLib.getPropertyInt("video-params/h")
            val fps = MPVLib.getPropertyDouble("container-fps")?.toFloat()
            val codec = MPVLib.getPropertyString("video-codec")

            if (width != null && height != null) {
                Metadata.Video(
                    width = width,
                    height = height,
                    frameRate = fps,
                    mimeType = codec?.let { "video/$it" },
                    isSelected = true
                )
            } else null
        }.getOrNull()
    }

    private fun extractAudioMetadata(): Metadata.Audio? {
        return runCatching {
            val channels = MPVLib.getPropertyInt("audio-params/channels")
            val sampleRate = MPVLib.getPropertyInt("audio-params/samplerate")
            val codec = MPVLib.getPropertyString("audio-codec")

            Metadata.Audio(
                channels = channels,
                sampleRate = sampleRate,
                mimeType = codec?.let { "audio/$it" },
                isSelected = true
            )
        }.getOrNull()
    }

    private fun extractSubtitleMetadata(): Metadata.Subtitle? {
        return runCatching {
            val codec = MPVLib.getPropertyString("sub-codec")
            if (codec != null) {
                Metadata.Subtitle(
                    mimeType = "text/$codec",
                    isSelected = true
                )
            } else null
        }.getOrNull()
    }

    private fun extractVideoTracks(): List<Metadata.Video> {
        // MPV track extraction would require more complex property queries
        return emptyList()
    }

    private fun extractAudioTracks(): List<Metadata.Audio> {
        // MPV track extraction would require more complex property queries
        return emptyList()
    }

    private fun extractSubtitleTracks(): List<Metadata.Subtitle> {
        // MPV track extraction would require more complex property queries
        return emptyList()
    }
}

