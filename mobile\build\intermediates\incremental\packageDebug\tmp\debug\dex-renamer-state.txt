#Tue Aug 19 14:03:09 HKT 2025
path.4=1/classes.dex
path.3=14/classes.dex
path.2=12/classes.dex
path.1=10/classes.dex
path.8=5/classes.dex
path.7=4/classes.dex
path.6=3/classes.dex
path.5=2/classes.dex
path.0=classes.dex
base.4=C\:\\Users\\fly\\StudioProjects\\mytv-android\\mobile\\build\\intermediates\\dex\\debug\\mergeLibDexDebug\\1\\classes.dex
base.3=C\:\\Users\\fly\\StudioProjects\\mytv-android\\mobile\\build\\intermediates\\dex\\debug\\mergeLibDexDebug\\14\\classes.dex
base.2=C\:\\Users\\fly\\StudioProjects\\mytv-android\\mobile\\build\\intermediates\\dex\\debug\\mergeLibDexDebug\\12\\classes.dex
base.1=C\:\\Users\\fly\\StudioProjects\\mytv-android\\mobile\\build\\intermediates\\dex\\debug\\mergeLibDexDebug\\10\\classes.dex
base.0=C\:\\Users\\fly\\StudioProjects\\mytv-android\\mobile\\build\\intermediates\\dex\\debug\\mergeExtDexDebug\\classes.dex
path.9=6/classes.dex
base.9=C\:\\Users\\fly\\StudioProjects\\mytv-android\\mobile\\build\\intermediates\\dex\\debug\\mergeLibDexDebug\\6\\classes.dex
base.8=C\:\\Users\\fly\\StudioProjects\\mytv-android\\mobile\\build\\intermediates\\dex\\debug\\mergeLibDexDebug\\5\\classes.dex
base.7=C\:\\Users\\fly\\StudioProjects\\mytv-android\\mobile\\build\\intermediates\\dex\\debug\\mergeLibDexDebug\\4\\classes.dex
base.6=C\:\\Users\\fly\\StudioProjects\\mytv-android\\mobile\\build\\intermediates\\dex\\debug\\mergeLibDexDebug\\3\\classes.dex
base.5=C\:\\Users\\fly\\StudioProjects\\mytv-android\\mobile\\build\\intermediates\\dex\\debug\\mergeLibDexDebug\\2\\classes.dex
renamed.17=classes18.dex
renamed.9=classes10.dex
renamed.16=classes17.dex
renamed.8=classes9.dex
renamed.15=classes16.dex
renamed.14=classes15.dex
renamed.13=classes14.dex
renamed.12=classes13.dex
renamed.11=classes12.dex
renamed.10=classes11.dex
base.17=C\:\\Users\\fly\\StudioProjects\\mytv-android\\mobile\\build\\intermediates\\dex\\debug\\mergeExtDexDebug\\classes3.dex
base.16=C\:\\Users\\fly\\StudioProjects\\mytv-android\\mobile\\build\\intermediates\\dex\\debug\\mergeExtDexDebug\\classes2.dex
base.15=C\:\\Users\\fly\\StudioProjects\\mytv-android\\mobile\\build\\intermediates\\dex\\debug\\mergeProjectDexDebug\\7\\classes.dex
base.14=C\:\\Users\\fly\\StudioProjects\\mytv-android\\mobile\\build\\intermediates\\dex\\debug\\mergeProjectDexDebug\\15\\classes.dex
renamed.3=classes4.dex
path.12=0/classes.dex
renamed.2=classes3.dex
path.13=14/classes.dex
renamed.1=classes2.dex
path.10=7/classes.dex
renamed.0=classes.dex
path.11=9/classes.dex
renamed.7=classes8.dex
path.16=classes2.dex
renamed.6=classes7.dex
path.17=classes3.dex
renamed.5=classes6.dex
path.14=15/classes.dex
renamed.4=classes5.dex
path.15=7/classes.dex
base.13=C\:\\Users\\fly\\StudioProjects\\mytv-android\\mobile\\build\\intermediates\\dex\\debug\\mergeProjectDexDebug\\14\\classes.dex
base.12=C\:\\Users\\fly\\StudioProjects\\mytv-android\\mobile\\build\\intermediates\\dex\\debug\\mergeProjectDexDebug\\0\\classes.dex
base.11=C\:\\Users\\fly\\StudioProjects\\mytv-android\\mobile\\build\\intermediates\\dex\\debug\\mergeLibDexDebug\\9\\classes.dex
base.10=C\:\\Users\\fly\\StudioProjects\\mytv-android\\mobile\\build\\intermediates\\dex\\debug\\mergeLibDexDebug\\7\\classes.dex
