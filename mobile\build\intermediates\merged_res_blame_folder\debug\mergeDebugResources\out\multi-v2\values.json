{"logs": [{"outputFile": "top.yogiczy.mytv.mobile-mergeDebugResources-60:/values/values.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\c98e1a41ddbd0f58bbf4b7505ad9616c\\transformed\\appcompat-1.7.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,221,222,226,230,234,239,245,252,256,260,265,269,273,277,281,285,289,295,299,305,309,315,319,324,328,331,335,341,345,351,355,361,364,368,372,376,380,384,385,386,387,390,393,396,399,403,404,405,406,407,410,412,414,416,421,422,426,432,436,437,439,451,452,456,462,466,467,468,472,499,503,504,508,536,708,734,905,931,962,970,976,992,1014,1019,1024,1034,1043,1052,1056,1063,1082,1089,1090,1099,1102,1105,1109,1113,1117,1120,1121,1126,1131,1141,1146,1153,1159,1160,1163,1167,1172,1174,1176,1179,1182,1184,1188,1191,1198,1201,1204,1208,1210,1214,1216,1218,1220,1224,1232,1240,1252,1258,1267,1270,1281,1284,1285,1290,1291,1296,1365,1435,1436,1446,1455,1456,1458,1462,1465,1468,1471,1474,1477,1480,1483,1487,1490,1493,1496,1500,1503,1507,1511,1512,1513,1514,1515,1516,1517,1518,1519,1520,1521,1522,1523,1524,1525,1526,1527,1528,1529,1530,1531,1533,1535,1536,1537,1538,1539,1540,1541,1542,1544,1545,1547,1548,1550,1552,1553,1555,1556,1557,1558,1559,1560,1562,1563,1564,1565,1566,1567,1569,1571,1573,1574,1575,1576,1577,1578,1579,1580,1581,1582,1583,1584,1585,1587,1588,1589,1590,1591,1592,1593,1595,1599,1603,1604,1605,1606,1607,1608,1612,1613,1614,1615,1617,1619,1621,1623,1625,1626,1627,1628,1630,1632,1634,1635,1636,1637,1638,1639,1640,1641,1642,1643,1644,1645,1648,1649,1650,1651,1653,1655,1656,1658,1659,1661,1663,1665,1666,1667,1668,1669,1670,1671,1672,1673,1674,1675,1676,1678,1679,1680,1681,1683,1684,1685,1686,1687,1689,1691,1693,1695,1696,1697,1698,1699,1700,1701,1702,1703,1704,1705,1706,1707,1708,1709,1710,1785,1788,1791,1794,1808,1814,1824,1827,1856,1883,1892,1956,2319,2323,2351,2379,2397,2421,2427,2433,2454,2578,2598,2604,2608,2614,2649,2661,2727,2747,2802,2814,2840", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,160,205,254,295,350,412,476,546,607,682,758,835,913,998,1080,1156,1232,1309,1387,1493,1599,1678,1758,1815,1873,1947,2022,2087,2153,2213,2274,2346,2419,2486,2554,2613,2672,2731,2790,2849,2903,2957,3010,3064,3118,3172,3226,3300,3379,3452,3526,3597,3669,3741,3814,3871,3929,4002,4076,4150,4225,4297,4370,4440,4511,4571,4632,4701,4770,4840,4914,4990,5054,5131,5207,5284,5349,5418,5495,5570,5639,5707,5784,5850,5911,6008,6073,6142,6241,6312,6371,6429,6486,6545,6609,6680,6752,6824,6896,6968,7035,7103,7171,7230,7293,7357,7447,7538,7598,7664,7731,7797,7867,7931,7984,8051,8112,8179,8292,8350,8413,8478,8543,8618,8691,8763,8807,8854,8900,8949,9010,9071,9132,9194,9258,9322,9386,9451,9514,9574,9635,9701,9760,9820,9882,9953,10013,10081,10167,10254,10344,10431,10519,10601,10684,10774,10865,10917,10975,11020,11086,11150,11207,11264,11318,11375,11423,11472,11523,11557,11604,11653,11699,11731,11795,11857,11917,11974,12048,12118,12196,12250,12320,12405,12453,12499,12560,12623,12689,12753,12824,12887,12952,13016,13077,13138,13190,13263,13337,13406,13481,13555,13629,13770,13840,13893,13971,14061,14149,14245,14335,14917,15006,15253,15534,15786,16071,16464,16941,17163,17385,17661,17888,18118,18348,18578,18808,19035,19454,19680,20105,20335,20763,20982,21265,21473,21604,21831,22257,22482,22909,23130,23555,23675,23951,24252,24576,24867,25181,25318,25449,25554,25796,25963,26167,26375,26646,26758,26870,26975,27092,27306,27452,27592,27678,28026,28114,28360,28778,29027,29109,29207,29864,29964,30216,30640,30895,30989,31078,31315,33339,33581,33683,33936,36092,46773,48289,58984,60512,62269,62895,63315,64576,65841,66097,66333,66880,67374,67979,68177,68757,70125,70500,70618,71156,71313,71509,71782,72038,72208,72349,72413,72778,73145,73821,74085,74423,74776,74870,75056,75362,75624,75749,75876,76115,76326,76445,76638,76815,77270,77451,77573,77832,77945,78132,78234,78341,78470,78745,79253,79749,80626,80920,81490,81639,82371,82543,82627,82963,83055,83333,88564,93935,93997,94575,95159,95250,95363,95592,95752,95904,96075,96241,96410,96577,96740,96983,97153,97326,97497,97771,97970,98175,98505,98589,98685,98781,98879,98979,99081,99183,99285,99387,99489,99589,99685,99797,99926,100049,100180,100311,100409,100523,100617,100757,100891,100987,101099,101199,101315,101411,101523,101623,101763,101899,102063,102193,102351,102501,102642,102786,102921,103033,103183,103311,103439,103575,103707,103837,103967,104079,104219,104365,104509,104647,104713,104803,104879,104983,105073,105175,105283,105391,105491,105571,105663,105761,105871,105923,106001,106107,106199,106303,106413,106535,106698,106855,106935,107035,107125,107235,107325,107566,107660,107766,107858,107958,108070,108184,108300,108416,108510,108624,108736,108838,108958,109080,109162,109266,109386,109512,109610,109704,109792,109904,110020,110142,110254,110429,110545,110631,110723,110835,110959,111026,111152,111220,111348,111492,111620,111689,111784,111899,112012,112111,112220,112331,112442,112543,112648,112748,112878,112969,113092,113186,113298,113384,113488,113584,113672,113790,113894,113998,114124,114212,114320,114420,114510,114620,114704,114806,114890,114944,115008,115114,115200,115310,115394,115514,118130,118248,118363,118443,118804,119037,119554,119632,120976,122337,122725,125568,135621,135756,137126,138483,139055,139806,140068,140268,140647,144925,145531,145760,145911,146126,147209,147521,150547,151291,153422,153762,155073", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,220,221,225,229,233,238,244,251,255,259,264,268,272,276,280,284,288,294,298,304,308,314,318,323,327,330,334,340,344,350,354,360,363,367,371,375,379,383,384,385,386,389,392,395,398,402,403,404,405,406,409,411,413,415,420,421,425,431,435,436,438,450,451,455,461,465,466,467,471,498,502,503,507,535,707,733,904,930,961,969,975,991,1013,1018,1023,1033,1042,1051,1055,1062,1081,1088,1089,1098,1101,1104,1108,1112,1116,1119,1120,1125,1130,1140,1145,1152,1158,1159,1162,1166,1171,1173,1175,1178,1181,1183,1187,1190,1197,1200,1203,1207,1209,1213,1215,1217,1219,1223,1231,1239,1251,1257,1266,1269,1280,1283,1284,1289,1290,1295,1364,1434,1435,1445,1454,1455,1457,1461,1464,1467,1470,1473,1476,1479,1482,1486,1489,1492,1495,1499,1502,1506,1510,1511,1512,1513,1514,1515,1516,1517,1518,1519,1520,1521,1522,1523,1524,1525,1526,1527,1528,1529,1530,1532,1534,1535,1536,1537,1538,1539,1540,1541,1543,1544,1546,1547,1549,1551,1552,1554,1555,1556,1557,1558,1559,1561,1562,1563,1564,1565,1566,1568,1570,1572,1573,1574,1575,1576,1577,1578,1579,1580,1581,1582,1583,1584,1586,1587,1588,1589,1590,1591,1592,1594,1598,1602,1603,1604,1605,1606,1607,1611,1612,1613,1614,1616,1618,1620,1622,1624,1625,1626,1627,1629,1631,1633,1634,1635,1636,1637,1638,1639,1640,1641,1642,1643,1644,1647,1648,1649,1650,1652,1654,1655,1657,1658,1660,1662,1664,1665,1666,1667,1668,1669,1670,1671,1672,1673,1674,1675,1677,1678,1679,1680,1682,1683,1684,1685,1686,1688,1690,1692,1694,1695,1696,1697,1698,1699,1700,1701,1702,1703,1704,1705,1706,1707,1708,1709,1784,1787,1790,1793,1807,1813,1823,1826,1855,1882,1891,1955,2318,2322,2350,2378,2396,2420,2426,2432,2453,2577,2597,2603,2607,2613,2648,2660,2726,2746,2801,2813,2839,2846", "endColumns": "54,44,48,40,54,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,43,46,45,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "155,200,249,290,345,407,471,541,602,677,753,830,908,993,1075,1151,1227,1304,1382,1488,1594,1673,1753,1810,1868,1942,2017,2082,2148,2208,2269,2341,2414,2481,2549,2608,2667,2726,2785,2844,2898,2952,3005,3059,3113,3167,3221,3295,3374,3447,3521,3592,3664,3736,3809,3866,3924,3997,4071,4145,4220,4292,4365,4435,4506,4566,4627,4696,4765,4835,4909,4985,5049,5126,5202,5279,5344,5413,5490,5565,5634,5702,5779,5845,5906,6003,6068,6137,6236,6307,6366,6424,6481,6540,6604,6675,6747,6819,6891,6963,7030,7098,7166,7225,7288,7352,7442,7533,7593,7659,7726,7792,7862,7926,7979,8046,8107,8174,8287,8345,8408,8473,8538,8613,8686,8758,8802,8849,8895,8944,9005,9066,9127,9189,9253,9317,9381,9446,9509,9569,9630,9696,9755,9815,9877,9948,10008,10076,10162,10249,10339,10426,10514,10596,10679,10769,10860,10912,10970,11015,11081,11145,11202,11259,11313,11370,11418,11467,11518,11552,11599,11648,11694,11726,11790,11852,11912,11969,12043,12113,12191,12245,12315,12400,12448,12494,12555,12618,12684,12748,12819,12882,12947,13011,13072,13133,13185,13258,13332,13401,13476,13550,13624,13765,13835,13888,13966,14056,14144,14240,14330,14912,15001,15248,15529,15781,16066,16459,16936,17158,17380,17656,17883,18113,18343,18573,18803,19030,19449,19675,20100,20330,20758,20977,21260,21468,21599,21826,22252,22477,22904,23125,23550,23670,23946,24247,24571,24862,25176,25313,25444,25549,25791,25958,26162,26370,26641,26753,26865,26970,27087,27301,27447,27587,27673,28021,28109,28355,28773,29022,29104,29202,29859,29959,30211,30635,30890,30984,31073,31310,33334,33576,33678,33931,36087,46768,48284,58979,60507,62264,62890,63310,64571,65836,66092,66328,66875,67369,67974,68172,68752,70120,70495,70613,71151,71308,71504,71777,72033,72203,72344,72408,72773,73140,73816,74080,74418,74771,74865,75051,75357,75619,75744,75871,76110,76321,76440,76633,76810,77265,77446,77568,77827,77940,78127,78229,78336,78465,78740,79248,79744,80621,80915,81485,81634,82366,82538,82622,82958,83050,83328,88559,93930,93992,94570,95154,95245,95358,95587,95747,95899,96070,96236,96405,96572,96735,96978,97148,97321,97492,97766,97965,98170,98500,98584,98680,98776,98874,98974,99076,99178,99280,99382,99484,99584,99680,99792,99921,100044,100175,100306,100404,100518,100612,100752,100886,100982,101094,101194,101310,101406,101518,101618,101758,101894,102058,102188,102346,102496,102637,102781,102916,103028,103178,103306,103434,103570,103702,103832,103962,104074,104214,104360,104504,104642,104708,104798,104874,104978,105068,105170,105278,105386,105486,105566,105658,105756,105866,105918,105996,106102,106194,106298,106408,106530,106693,106850,106930,107030,107120,107230,107320,107561,107655,107761,107853,107953,108065,108179,108295,108411,108505,108619,108731,108833,108953,109075,109157,109261,109381,109507,109605,109699,109787,109899,110015,110137,110249,110424,110540,110626,110718,110830,110954,111021,111147,111215,111343,111487,111615,111684,111779,111894,112007,112106,112215,112326,112437,112538,112643,112743,112873,112964,113087,113181,113293,113379,113483,113579,113667,113785,113889,113993,114119,114207,114315,114415,114505,114615,114699,114801,114885,114939,115003,115109,115195,115305,115389,115509,118125,118243,118358,118438,118799,119032,119549,119627,120971,122332,122720,125563,135616,135751,137121,138478,139050,139801,140063,140263,140642,144920,145526,145755,145906,146121,147204,147516,150542,151286,153417,153757,155068,155271"}, "to": {"startLines": "2,3,4,6,7,8,9,10,11,12,13,14,15,18,19,20,21,22,23,24,25,26,27,28,29,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,164,165,166,167,168,169,170,171,172,188,189,190,191,192,193,194,195,231,232,233,234,241,248,249,252,269,276,277,278,279,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,390,401,402,403,404,405,406,414,415,419,423,427,432,438,445,449,453,458,462,466,470,474,478,482,488,492,498,502,508,512,517,521,524,528,534,538,544,548,554,557,561,565,569,573,577,578,579,580,583,586,589,592,596,597,598,599,600,603,605,607,609,614,615,619,625,629,630,632,644,645,649,655,659,660,661,665,692,696,697,701,729,901,927,1098,1124,1155,1163,1169,1185,1207,1212,1217,1227,1236,1245,1249,1256,1275,1282,1283,1292,1295,1298,1302,1306,1310,1313,1314,1319,1324,1334,1339,1346,1352,1353,1356,1360,1365,1367,1369,1372,1375,1377,1381,1384,1391,1394,1397,1401,1403,1407,1409,1411,1413,1417,1425,1433,1445,1451,1460,1463,1474,1477,1478,1483,1484,1512,1581,1651,1652,1662,1671,1672,1674,1678,1681,1684,1687,1690,1693,1696,1699,1703,1706,1709,1712,1716,1719,1723,1727,1728,1729,1730,1731,1732,1733,1734,1735,1736,1737,1738,1739,1740,1741,1742,1743,1744,1745,1746,1747,1749,1751,1752,1753,1754,1755,1756,1757,1758,1760,1761,1763,1764,1766,1768,1769,1771,1772,1773,1774,1775,1776,1778,1779,1780,1781,1782,1794,1796,1798,1800,1801,1802,1803,1804,1805,1806,1807,1808,1809,1810,1811,1812,1814,1815,1816,1817,1818,1819,1820,1822,1826,1831,1832,1833,1834,1835,1836,1840,1841,1842,1843,1845,1847,1849,1851,1853,1854,1855,1856,1858,1860,1862,1863,1864,1865,1866,1867,1868,1869,1870,1871,1872,1873,1876,1877,1878,1879,1881,1883,1884,1886,1887,1889,1891,1893,1894,1895,1896,1897,1898,1899,1900,1901,1902,1903,1904,1906,1907,1908,1909,1911,1912,1913,1914,1915,1917,1919,1921,1923,1924,1925,1926,1927,1928,1929,1930,1931,1932,1933,1934,1935,1936,1937,1940,2015,2018,2021,2024,2038,2051,2093,2096,2125,2152,2161,2225,2588,2598,2636,2664,2784,2808,2814,2820,2841,2965,3024,3030,3034,3040,3075,3107,3173,3193,3248,3260,3286", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,205,250,359,400,455,517,581,651,712,787,863,940,1178,1263,1345,1421,1497,1574,1652,1758,1864,1943,2023,2080,2269,2343,2418,2483,2549,2609,2670,2742,2815,2882,2950,3009,3068,3127,3186,3245,3299,3353,3406,3460,3514,3568,3754,3828,3907,3980,4054,4125,4197,4269,4342,4399,4457,4530,4604,4678,4753,4825,4898,4968,5039,5099,5160,5229,5298,5368,5442,5518,5582,5659,5735,5812,5877,5946,6023,6098,6167,6235,6312,6378,6439,6536,6601,6670,6769,6840,6899,6957,7014,7073,7137,7208,7280,7352,7424,7496,7563,7631,7699,7758,7821,7885,7975,8066,8126,8192,8259,8325,8395,8459,8512,8579,8640,8707,8820,8878,8941,9006,9071,9146,9219,9291,9335,9382,9428,9477,9538,9599,9660,9722,9786,9850,9914,9979,10042,10102,10163,10229,10288,10348,10410,10481,10541,11097,11183,11270,11360,11447,11535,11617,11700,11790,12859,12911,12969,13014,13080,13144,13201,13258,15435,15492,15540,15589,16001,16334,16381,16537,17442,17798,17862,17924,17984,18184,18258,18328,18406,18460,18530,18615,18663,18709,18770,18833,18899,18963,19034,19097,19162,19226,19287,19348,19400,19473,19547,19616,19691,19765,19839,19980,25803,26385,26463,26553,26641,26737,26827,27409,27498,27745,28026,28278,28563,28956,29433,29655,29877,30153,30380,30610,30840,31070,31300,31527,31946,32172,32597,32827,33255,33474,33757,33965,34096,34323,34749,34974,35401,35622,36047,36167,36443,36744,37068,37359,37673,37810,37941,38046,38288,38455,38659,38867,39138,39250,39362,39467,39584,39798,39944,40084,40170,40518,40606,40852,41270,41519,41601,41699,42356,42456,42708,43132,43387,43481,43570,43807,45831,46073,46175,46428,48584,59265,60781,71476,73004,74761,75387,75807,77068,78333,78589,78825,79372,79866,80471,80669,81249,82617,82992,83110,83648,83805,84001,84274,84530,84700,84841,84905,85270,85637,86313,86577,86915,87268,87362,87548,87854,88116,88241,88368,88607,88818,88937,89130,89307,89762,89943,90065,90324,90437,90624,90726,90833,90962,91237,91745,92241,93118,93412,93982,94131,94863,95035,95119,95455,95547,97111,102342,107713,107775,108353,108937,109028,109141,109370,109530,109682,109853,110019,110188,110355,110518,110761,110931,111104,111275,111549,111748,111953,112283,112367,112463,112559,112657,112757,112859,112961,113063,113165,113267,113367,113463,113575,113704,113827,113958,114089,114187,114301,114395,114535,114669,114765,114877,114977,115093,115189,115301,115401,115541,115677,115841,115971,116129,116279,116420,116564,116699,116811,116961,117089,117217,117353,117485,117615,117745,117857,118755,118901,119045,119183,119249,119339,119415,119519,119609,119711,119819,119927,120027,120107,120199,120297,120407,120459,120537,120643,120735,120839,120949,121071,121234,121472,121552,121652,121742,121852,121942,122183,122277,122383,122475,122575,122687,122801,122917,123033,123127,123241,123353,123455,123575,123697,123779,123883,124003,124129,124227,124321,124409,124521,124637,124759,124871,125046,125162,125248,125340,125452,125576,125643,125769,125837,125965,126109,126237,126306,126401,126516,126629,126728,126837,126948,127059,127160,127265,127365,127495,127586,127709,127803,127915,128001,128105,128201,128289,128407,128511,128615,128741,128829,128937,129037,129127,129237,129321,129423,129507,129561,129625,129731,129817,129927,130011,130270,132886,133004,133119,133199,133560,134097,135501,135579,136923,138284,138672,141515,151568,151906,153577,154934,159086,159837,160099,160299,160678,164956,167237,167466,167617,167832,168915,169765,172791,173535,175666,176006,177317", "endLines": "2,3,4,6,7,8,9,10,11,12,13,14,15,18,19,20,21,22,23,24,25,26,27,28,29,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,164,165,166,167,168,169,170,171,172,188,189,190,191,192,193,194,195,231,232,233,234,241,248,249,252,269,276,277,278,279,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,390,401,402,403,404,405,413,414,418,422,426,431,437,444,448,452,457,461,465,469,473,477,481,487,491,497,501,507,511,516,520,523,527,533,537,543,547,553,556,560,564,568,572,576,577,578,579,582,585,588,591,595,596,597,598,599,602,604,606,608,613,614,618,624,628,629,631,643,644,648,654,658,659,660,664,691,695,696,700,728,900,926,1097,1123,1154,1162,1168,1184,1206,1211,1216,1226,1235,1244,1248,1255,1274,1281,1282,1291,1294,1297,1301,1305,1309,1312,1313,1318,1323,1333,1338,1345,1351,1352,1355,1359,1364,1366,1368,1371,1374,1376,1380,1383,1390,1393,1396,1400,1402,1406,1408,1410,1412,1416,1424,1432,1444,1450,1459,1462,1473,1476,1477,1482,1483,1488,1580,1650,1651,1661,1670,1671,1673,1677,1680,1683,1686,1689,1692,1695,1698,1702,1705,1708,1711,1715,1718,1722,1726,1727,1728,1729,1730,1731,1732,1733,1734,1735,1736,1737,1738,1739,1740,1741,1742,1743,1744,1745,1746,1748,1750,1751,1752,1753,1754,1755,1756,1757,1759,1760,1762,1763,1765,1767,1768,1770,1771,1772,1773,1774,1775,1777,1778,1779,1780,1781,1782,1795,1797,1799,1800,1801,1802,1803,1804,1805,1806,1807,1808,1809,1810,1811,1813,1814,1815,1816,1817,1818,1819,1821,1825,1829,1831,1832,1833,1834,1835,1839,1840,1841,1842,1844,1846,1848,1850,1852,1853,1854,1855,1857,1859,1861,1862,1863,1864,1865,1866,1867,1868,1869,1870,1871,1872,1875,1876,1877,1878,1880,1882,1883,1885,1886,1888,1890,1892,1893,1894,1895,1896,1897,1898,1899,1900,1901,1902,1903,1905,1906,1907,1908,1910,1911,1912,1913,1914,1916,1918,1920,1922,1923,1924,1925,1926,1927,1928,1929,1930,1931,1932,1933,1934,1935,1936,1937,2014,2017,2020,2023,2037,2043,2060,2095,2124,2151,2160,2224,2587,2591,2625,2663,2681,2807,2813,2819,2840,2964,2984,3029,3033,3039,3074,3086,3172,3192,3247,3259,3285,3292", "endColumns": "54,44,48,40,54,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,43,46,45,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "200,245,294,395,450,512,576,646,707,782,858,935,1013,1258,1340,1416,1492,1569,1647,1753,1859,1938,2018,2075,2133,2338,2413,2478,2544,2604,2665,2737,2810,2877,2945,3004,3063,3122,3181,3240,3294,3348,3401,3455,3509,3563,3617,3823,3902,3975,4049,4120,4192,4264,4337,4394,4452,4525,4599,4673,4748,4820,4893,4963,5034,5094,5155,5224,5293,5363,5437,5513,5577,5654,5730,5807,5872,5941,6018,6093,6162,6230,6307,6373,6434,6531,6596,6665,6764,6835,6894,6952,7009,7068,7132,7203,7275,7347,7419,7491,7558,7626,7694,7753,7816,7880,7970,8061,8121,8187,8254,8320,8390,8454,8507,8574,8635,8702,8815,8873,8936,9001,9066,9141,9214,9286,9330,9377,9423,9472,9533,9594,9655,9717,9781,9845,9909,9974,10037,10097,10158,10224,10283,10343,10405,10476,10536,10604,11178,11265,11355,11442,11530,11612,11695,11785,11876,12906,12964,13009,13075,13139,13196,13253,13307,15487,15535,15584,15635,16030,16376,16425,16578,17469,17857,17919,17979,18036,18253,18323,18401,18455,18525,18610,18658,18704,18765,18828,18894,18958,19029,19092,19157,19221,19282,19343,19395,19468,19542,19611,19686,19760,19834,19975,20045,25851,26458,26548,26636,26732,26822,27404,27493,27740,28021,28273,28558,28951,29428,29650,29872,30148,30375,30605,30835,31065,31295,31522,31941,32167,32592,32822,33250,33469,33752,33960,34091,34318,34744,34969,35396,35617,36042,36162,36438,36739,37063,37354,37668,37805,37936,38041,38283,38450,38654,38862,39133,39245,39357,39462,39579,39793,39939,40079,40165,40513,40601,40847,41265,41514,41596,41694,42351,42451,42703,43127,43382,43476,43565,43802,45826,46068,46170,46423,48579,59260,60776,71471,72999,74756,75382,75802,77063,78328,78584,78820,79367,79861,80466,80664,81244,82612,82987,83105,83643,83800,83996,84269,84525,84695,84836,84900,85265,85632,86308,86572,86910,87263,87357,87543,87849,88111,88236,88363,88602,88813,88932,89125,89302,89757,89938,90060,90319,90432,90619,90721,90828,90957,91232,91740,92236,93113,93407,93977,94126,94858,95030,95114,95450,95542,95820,102337,107708,107770,108348,108932,109023,109136,109365,109525,109677,109848,110014,110183,110350,110513,110756,110926,111099,111270,111544,111743,111948,112278,112362,112458,112554,112652,112752,112854,112956,113058,113160,113262,113362,113458,113570,113699,113822,113953,114084,114182,114296,114390,114530,114664,114760,114872,114972,115088,115184,115296,115396,115536,115672,115836,115966,116124,116274,116415,116559,116694,116806,116956,117084,117212,117348,117480,117610,117740,117852,117992,118896,119040,119178,119244,119334,119410,119514,119604,119706,119814,119922,120022,120102,120194,120292,120402,120454,120532,120638,120730,120834,120944,121066,121229,121386,121547,121647,121737,121847,121937,122178,122272,122378,122470,122570,122682,122796,122912,123028,123122,123236,123348,123450,123570,123692,123774,123878,123998,124124,124222,124316,124404,124516,124632,124754,124866,125041,125157,125243,125335,125447,125571,125638,125764,125832,125960,126104,126232,126301,126396,126511,126624,126723,126832,126943,127054,127155,127260,127360,127490,127581,127704,127798,127910,127996,128100,128196,128284,128402,128506,128610,128736,128824,128932,129032,129122,129232,129316,129418,129502,129556,129620,129726,129812,129922,130006,130126,132881,132999,133114,133194,133555,133788,134609,135574,136918,138279,138667,141510,151563,151698,153271,154929,155501,159832,160094,160294,160673,164951,165557,167461,167612,167827,168910,169222,172786,173530,175661,176001,177312,177515"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\c7360db8db21da0cd2a2513de2c76e03\\transformed\\lifecycle-runtime-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "270", "startColumns": "4", "startOffsets": "17474", "endColumns": "42", "endOffsets": "17512"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\e27b26ab4fdbef454305ada43ffa6aaf\\transformed\\navigation-common-2.8.3\\res\\values\\values.xml", "from": {"startLines": "2,15,21,27,30", "startColumns": "4,4,4,4,4", "startOffsets": "55,694,938,1185,1318", "endLines": "14,20,26,29,34", "endColumns": "24,24,24,24,24", "endOffsets": "689,933,1180,1313,1495"}, "to": {"startLines": "2985,2998,3004,3010,3019", "startColumns": "4,4,4,4,4", "startOffsets": "165562,166201,166445,166692,167055", "endLines": "2997,3003,3009,3012,3023", "endColumns": "24,24,24,24,24", "endOffsets": "166196,166440,166687,166820,167232"}}, {"source": "C:\\Users\\<USER>\\StudioProjects\\mytv-android\\mobile\\src\\main\\res\\values\\themes.xml", "from": {"startLines": "3", "startColumns": "4", "startOffsets": "59", "endColumns": "81", "endOffsets": "136"}, "to": {"startLines": "1830", "startColumns": "4", "startOffsets": "121391", "endColumns": "80", "endOffsets": "121467"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\4c124e34a7f4172b432f9fe272bec823\\transformed\\activity-1.9.3\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,97", "endColumns": "41,59", "endOffsets": "92,152"}, "to": {"startLines": "250,271", "startColumns": "4,4", "startOffsets": "16430,17517", "endColumns": "41,59", "endOffsets": "16467,17572"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\018c63b1485334add67611329c38a7ad\\transformed\\appcompat-resources-1.7.0\\res\\values\\values.xml", "from": {"startLines": "2,18,24,34,50", "startColumns": "4,4,4,4,4", "startOffsets": "55,480,658,942,1353", "endLines": "17,23,33,49,53", "endColumns": "24,24,24,24,24", "endOffsets": "475,653,937,1348,1475"}, "to": {"startLines": "2061,2077,2083,3087,3103", "startColumns": "4,4,4,4,4", "startOffsets": "134614,135039,135217,169227,169638", "endLines": "2076,2082,2092,3102,3106", "endColumns": "24,24,24,24,24", "endOffsets": "135034,135212,135496,169633,169760"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\55d105b608835fb0a5975933fd0070b6\\transformed\\core-1.13.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,98,99,103,104,105,106,112,122,155,176,209", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,340,406,475,538,608,676,748,818,879,953,1026,1087,1148,1210,1274,1336,1397,1465,1565,1625,1691,1764,1833,1890,1942,2004,2076,2152,4127,4162,4197,4252,4315,4370,4428,4486,4547,4610,4667,4718,4768,4829,4886,4952,4986,5021,5056,5126,5193,5265,5334,5403,5477,5549,5637,5708,5825,6026,6136,6337,6466,6538,6605,6808,7109,8840,9521,10203", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,97,98,102,103,104,105,111,121,154,175,208,214", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,335,401,470,533,603,671,743,813,874,948,1021,1082,1143,1205,1269,1331,1392,1460,1560,1620,1686,1759,1828,1885,1937,1999,2071,2147,2212,4157,4192,4247,4310,4365,4423,4481,4542,4605,4662,4713,4763,4824,4881,4947,4981,5016,5051,5121,5188,5260,5329,5398,5472,5544,5632,5703,5820,6021,6131,6332,6461,6533,6600,6803,7104,8835,9516,10198,10365"}, "to": {"startLines": "5,16,17,30,31,54,55,157,158,159,160,161,162,163,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,196,197,198,244,245,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,281,311,312,313,314,315,316,317,395,1783,1784,1788,1789,1793,1938,1939,2592,2626,2682,2715,2745,2778", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "299,1018,1090,2138,2203,3622,3691,10609,10679,10747,10819,10889,10950,11024,11881,11942,12003,12065,12129,12191,12252,12320,12420,12480,12546,12619,12688,12745,12797,13312,13384,13460,16144,16179,16583,16638,16701,16756,16814,16872,16933,16996,17053,17104,17154,17215,17272,17338,17372,17407,18114,20175,20242,20314,20383,20452,20526,20598,26030,117997,118114,118315,118425,118626,130131,130203,151703,153276,155506,157237,158237,158919", "endLines": "5,16,17,30,31,54,55,157,158,159,160,161,162,163,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,196,197,198,244,245,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,281,311,312,313,314,315,316,317,395,1783,1787,1788,1792,1793,1938,1939,2597,2635,2714,2735,2777,2783", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "354,1085,1173,2198,2264,3686,3749,10674,10742,10814,10884,10945,11019,11092,11937,11998,12060,12124,12186,12247,12315,12415,12475,12541,12614,12683,12740,12792,12854,13379,13455,13520,16174,16209,16633,16696,16751,16809,16867,16928,16991,17048,17099,17149,17210,17267,17333,17367,17402,17437,18179,20237,20309,20378,20447,20521,20593,20681,26096,118109,118310,118420,118621,118750,130198,130265,151901,153572,157232,157913,158914,159081"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\00b00a57a1321b3abddaddf7bed980a7\\transformed\\navigation-runtime-2.8.3\\res\\values\\values.xml", "from": {"startLines": "2,3,10,13", "startColumns": "4,4,4,4", "startOffsets": "55,108,412,527", "endLines": "2,9,12,15", "endColumns": "52,24,24,24", "endOffsets": "103,407,522,637"}, "to": {"startLines": "246,2044,3013,3016", "startColumns": "4,4,4,4", "startOffsets": "16214,133793,166825,166940", "endLines": "246,2050,3015,3018", "endColumns": "52,24,24,24", "endOffsets": "16262,134092,166935,167050"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\bdcbc52a7f4fc574f82c6a94b68ef448\\transformed\\lifecycle-viewmodel-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "273", "startColumns": "4", "startOffsets": "17631", "endColumns": "49", "endOffsets": "17676"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\fd2de9df1fcb7d47d0f664953b17c729\\transformed\\foundation-release\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,111", "endColumns": "55,54", "endOffsets": "106,161"}, "to": {"startLines": "399,400", "startColumns": "4,4", "startOffsets": "26274,26330", "endColumns": "55,54", "endOffsets": "26325,26380"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\1a24962cf41916db9572a74a797a0e06\\transformed\\savedstate-1.2.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "53", "endOffsets": "104"}, "to": {"startLines": "272", "startColumns": "4", "startOffsets": "17577", "endColumns": "53", "endOffsets": "17626"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\2beb69d6875208f2d04111a35001bba9\\transformed\\ui-graphics-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "65", "endOffsets": "116"}, "to": {"startLines": "239", "startColumns": "4", "startOffsets": "15884", "endColumns": "65", "endOffsets": "15945"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\3e8e26f56d8e20e616f6e0fb0878dba9\\transformed\\customview-poolingcontainer-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,109", "endColumns": "53,66", "endOffsets": "104,171"}, "to": {"startLines": "243,247", "startColumns": "4,4", "startOffsets": "16090,16267", "endColumns": "53,66", "endOffsets": "16139,16329"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\f0ce41bc1ab5a7ccfdd795c249b2c5b2\\transformed\\ui-release\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,61,64", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,209,268,328,388,448,508,568,628,688,748,808,868,927,987,1047,1107,1167,1227,1287,1347,1407,1467,1527,1586,1646,1706,1765,1824,1883,1942,2001,2060,2134,2192,2247,2298,2353,2406,2471,2525,2591,2692,2750,2802,2862,2924,2978,3028,3082,3128,3174,3216,3256,3303,3339,3429,3541,3652", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,60,63,67", "endColumns": "58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,49,53,45,45,41,39,46,35,89,12,12,12", "endOffsets": "204,263,323,383,443,503,563,623,683,743,803,863,922,982,1042,1102,1162,1222,1282,1342,1402,1462,1522,1581,1641,1701,1760,1819,1878,1937,1996,2055,2129,2187,2242,2293,2348,2401,2466,2520,2586,2687,2745,2797,2857,2919,2973,3023,3077,3123,3169,3211,3251,3298,3334,3424,3536,3647,3842"}, "to": {"startLines": "199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,235,236,237,240,242,275,318,319,320,321,322,323,324,386,387,388,389,391,392,393,394,396,397,398,1489,1505,1508", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "13525,13584,13643,13703,13763,13823,13883,13943,14003,14063,14123,14183,14243,14302,14362,14422,14482,14542,14602,14662,14722,14782,14842,14902,14961,15021,15081,15140,15199,15258,15317,15376,15640,15714,15772,15950,16035,17745,20686,20751,20805,20871,20972,21030,21082,25583,25645,25699,25749,25856,25902,25948,25990,26101,26148,26184,95825,96805,96916", "endLines": "199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,235,236,237,240,242,275,318,319,320,321,322,323,324,386,387,388,389,391,392,393,394,396,397,398,1491,1507,1511", "endColumns": "58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,49,53,45,45,41,39,46,35,89,12,12,12", "endOffsets": "13579,13638,13698,13758,13818,13878,13938,13998,14058,14118,14178,14238,14297,14357,14417,14477,14537,14597,14657,14717,14777,14837,14897,14956,15016,15076,15135,15194,15253,15312,15371,15430,15709,15767,15822,15996,16085,17793,20746,20800,20866,20967,21025,21077,21137,25640,25694,25744,25798,25897,25943,25985,26025,26143,26179,26269,95932,96911,97106"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\02a2dc3fb6f2f628d8f50a1ef0801c78\\transformed\\startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "309", "startColumns": "4", "startOffsets": "20050", "endColumns": "82", "endOffsets": "20128"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\e3284905063e776d62ae70595f5b34b4\\transformed\\fragment-1.5.4\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,10", "startColumns": "4,4,4,4,4", "startOffsets": "55,112,177,241,411", "endLines": "2,3,4,9,13", "endColumns": "56,64,63,24,24", "endOffsets": "107,172,236,406,555"}, "to": {"startLines": "238,251,274,2736,2741", "startColumns": "4,4,4,4,4", "startOffsets": "15827,16472,17681,157918,158088", "endLines": "238,251,274,2740,2744", "endColumns": "56,64,63,24,24", "endOffsets": "15879,16532,17740,158083,158232"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\136feb759fbdaa722f3905caf7f29ff7\\transformed\\material3-release\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,15,16,17,18,19,20,21,22,23,24,25,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,74", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,173,261,347,428,512,581,646,729,835,921,1041,1095,1164,1225,1294,1383,1478,1552,1649,1742,1840,1989,2080,2168,2264,2362,2426,2494,2581,2675,2742,2814,2886,2987,3096,3172,3241,3289,3355,3419,3493,3550,3607,3679,3729,3783,3854,3925,3995,4064,4122,4198,4269,4343,4429,4479,4549,4614,5329", "endLines": "2,3,4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,73,76", "endColumns": "72,87,85,80,83,68,64,82,105,85,13,53,68,60,68,88,94,73,96,92,97,13,90,87,95,97,63,67,86,93,66,71,71,100,108,75,68,47,65,63,73,56,56,71,49,53,70,70,69,68,57,75,70,73,85,49,69,64,12,12", "endOffsets": "168,256,342,423,507,576,641,724,830,916,1036,1090,1159,1220,1289,1378,1473,1547,1644,1737,1835,1984,2075,2163,2259,2357,2421,2489,2576,2670,2737,2809,2881,2982,3091,3167,3236,3284,3350,3414,3488,3545,3602,3674,3724,3778,3849,3920,3990,4059,4117,4193,4264,4338,4424,4474,4544,4609,5324,5477"}, "to": {"startLines": "280,325,326,327,328,329,330,331,332,333,334,337,338,339,340,341,342,343,344,345,346,347,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,1492,1502", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "18041,21142,21230,21316,21397,21481,21550,21615,21698,21804,21890,22010,22064,22133,22194,22263,22352,22447,22521,22618,22711,22809,22958,23049,23137,23233,23331,23395,23463,23550,23644,23711,23783,23855,23956,24065,24141,24210,24258,24324,24388,24462,24519,24576,24648,24698,24752,24823,24894,24964,25033,25091,25167,25238,25312,25398,25448,25518,95937,96652", "endLines": "280,325,326,327,328,329,330,331,332,333,336,337,338,339,340,341,342,343,344,345,346,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,1501,1504", "endColumns": "72,87,85,80,83,68,64,82,105,85,13,53,68,60,68,88,94,73,96,92,97,13,90,87,95,97,63,67,86,93,66,71,71,100,108,75,68,47,65,63,73,56,56,71,49,53,70,70,69,68,57,75,70,73,85,49,69,64,12,12", "endOffsets": "18109,21225,21311,21392,21476,21545,21610,21693,21799,21885,22005,22059,22128,22189,22258,22347,22442,22516,22613,22706,22804,22953,23044,23132,23228,23326,23390,23458,23545,23639,23706,23778,23850,23951,24060,24136,24205,24253,24319,24383,24457,24514,24571,24643,24693,24747,24818,24889,24959,25028,25086,25162,25233,25307,25393,25443,25513,25578,96647,96800"}}, {"source": "C:\\Users\\<USER>\\StudioProjects\\mytv-android\\mobile\\src\\main\\res\\values\\strings.xml", "from": {"startLines": "1", "startColumns": "4", "startOffsets": "17", "endColumns": "41", "endOffsets": "54"}, "to": {"startLines": "310", "startColumns": "4", "startOffsets": "20133", "endColumns": "41", "endOffsets": "20170"}}]}]}