# MPV播放器切换台卡住问题修复

## 问题分析

根据提供的日志分析，MPV播放器在切换频道时出现以下问题：

1. **EOF code: 3** - 播放结束
2. **avformat_open_input() failed** - 打开新流失败  
3. **Opening failed or was aborted** - 打开失败或被中止

## 根本原因

1. **缺少前一媒体项停止逻辑** - MPV播放器没有实现`videoPlayerStopPreviousMediaItem`配置支持
2. **资源冲突** - 在加载新流时没有正确清理前一个播放项，导致资源冲突
3. **错误处理不足** - 缺少适当的错误检测和重试机制
4. **网络超时设置不完善** - 缺少流打开超时和重连机制

## 修复方案

### 1. 添加前一媒体项停止支持

```kotlin
override fun prepare(line: ChannelLine) {
    runCatching {
        // Stop previous media item if configured
        if (Configs.videoPlayerStopPreviousMediaItem) {
            stop()
        }
        // ... rest of prepare logic
    }
}
```

### 2. 改进loadfile命令

使用`replace`模式确保清洁的转换：

```kotlin
// Load file with replace mode to ensure clean transition
MPVLib.command("loadfile", line.playableUrl, "replace")
```

### 3. 增强stop方法

```kotlin
override fun stop() {
    runCatching {
        // First pause to stop playback immediately
        MPVLib.setPropertyString("pause", "yes")
        // Then stop the current file
        MPVLib.command("stop")
        // Clear the playlist to ensure clean state
        MPVLib.command("playlist-clear")
    }
    // ... cleanup logic
}
```

### 4. 改进错误检测

在更新循环中添加早期错误检测：

```kotlin
// Check for playback errors early
if (!hasTriggeredReady && errorCheckCount < maxErrorChecks) {
    val eofReached = MPVLib.getPropertyBoolean("eof-reached") ?: false
    val coreIdle = MPVLib.getPropertyBoolean("core-idle") ?: false
    
    if (eofReached && coreIdle) {
        errorCheckCount++
        if (errorCheckCount >= maxErrorChecks) {
            triggerError(PlaybackException("MPV_ERROR_STREAM_FAILED", 20009))
            return@launch
        }
    }
}
```

### 5. 增强网络配置

添加更多网络超时和重连选项：

```kotlin
// Network and timeout options
MPVLib.setOptionString("network-timeout", Configs.videoPlayerLoadTimeout.toString())
MPVLib.setOptionString("stream-open-timeout", Configs.videoPlayerLoadTimeout.toString())

// Cache options for better streaming performance
MPVLib.setOptionString("cache-pause-initial", "yes")
MPVLib.setOptionString("cache-pause-wait", "3")

// Network retry options for better reliability
MPVLib.setOptionString("stream-lavf-o", "reconnect=1,reconnect_at_eof=1,reconnect_streamed=1,reconnect_delay_max=5")
```

### 6. 改进直播流检测

对于直播流，duration可能为null，改进检测逻辑：

```kotlin
// For live streams, duration might be null but time-pos should be available
if ((duration != null && duration > 0) || (timePos != null && timePos >= 0)) {
    hasTriggeredReady = true
    isPrepared = true
    // ... trigger ready events
}
```

## 测试验证

创建了`MpvVideoPlayerTest.kt`测试类来验证修复：

- 测试频道切换时的状态管理
- 测试多次快速切换的稳定性
- 测试播放控制操作
- 测试音量控制

## 预期效果

这些修复应该能够：

1. **减少切换台卡住** - 通过正确停止前一媒体项和清理资源
2. **提高网络稳定性** - 通过增强的超时和重连机制
3. **更好的错误处理** - 通过早期错误检测和适当的错误报告
4. **改善用户体验** - 通过更流畅的频道切换

## 使用建议

1. 确保在设置中启用"停止上一媒体项"选项
2. 对于网络不稳定的环境，可以适当增加`videoPlayerLoadTimeout`值
3. 如果仍有问题，可以尝试切换到Media3或IJK播放器作为备选方案
