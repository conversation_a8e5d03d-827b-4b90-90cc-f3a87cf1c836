1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="top.yogiczy.mytv.mobile"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10
11    <permission
11-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\55d105b608835fb0a5975933fd0070b6\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
12        android:name="top.yogiczy.mytv.mobile.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
12-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\55d105b608835fb0a5975933fd0070b6\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
13        android:protectionLevel="signature" />
13-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\55d105b608835fb0a5975933fd0070b6\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
14
15    <uses-permission android:name="top.yogiczy.mytv.mobile.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
15-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\55d105b608835fb0a5975933fd0070b6\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
15-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\55d105b608835fb0a5975933fd0070b6\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
16
17    <application
17-->C:\Users\<USER>\StudioProjects\mytv-android\mobile\src\main\AndroidManifest.xml:4:5-21:19
18        android:allowBackup="true"
18-->C:\Users\<USER>\StudioProjects\mytv-android\mobile\src\main\AndroidManifest.xml:5:9-35
19        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
19-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\55d105b608835fb0a5975933fd0070b6\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
20        android:debuggable="true"
21        android:extractNativeLibs="false"
22        android:icon="@mipmap/ic_launcher"
22-->C:\Users\<USER>\StudioProjects\mytv-android\mobile\src\main\AndroidManifest.xml:6:9-43
23        android:label="@string/app_name"
23-->C:\Users\<USER>\StudioProjects\mytv-android\mobile\src\main\AndroidManifest.xml:7:9-41
24        android:roundIcon="@mipmap/ic_launcher_round"
24-->C:\Users\<USER>\StudioProjects\mytv-android\mobile\src\main\AndroidManifest.xml:8:9-54
25        android:supportsRtl="true"
25-->C:\Users\<USER>\StudioProjects\mytv-android\mobile\src\main\AndroidManifest.xml:9:9-35
26        android:theme="@style/Theme.MyTV" >
26-->C:\Users\<USER>\StudioProjects\mytv-android\mobile\src\main\AndroidManifest.xml:10:9-42
27        <activity
27-->C:\Users\<USER>\StudioProjects\mytv-android\mobile\src\main\AndroidManifest.xml:11:9-20:20
28            android:name="top.yogiczy.mytv.mobile.MainActivity"
28-->C:\Users\<USER>\StudioProjects\mytv-android\mobile\src\main\AndroidManifest.xml:12:13-41
29            android:exported="true"
29-->C:\Users\<USER>\StudioProjects\mytv-android\mobile\src\main\AndroidManifest.xml:13:13-36
30            android:theme="@style/Theme.MyTV" >
30-->C:\Users\<USER>\StudioProjects\mytv-android\mobile\src\main\AndroidManifest.xml:14:13-46
31            <intent-filter>
31-->C:\Users\<USER>\StudioProjects\mytv-android\mobile\src\main\AndroidManifest.xml:15:13-19:29
32                <action android:name="android.intent.action.MAIN" />
32-->C:\Users\<USER>\StudioProjects\mytv-android\mobile\src\main\AndroidManifest.xml:16:17-69
32-->C:\Users\<USER>\StudioProjects\mytv-android\mobile\src\main\AndroidManifest.xml:16:25-66
33
34                <category android:name="android.intent.category.LAUNCHER" />
34-->C:\Users\<USER>\StudioProjects\mytv-android\mobile\src\main\AndroidManifest.xml:18:17-77
34-->C:\Users\<USER>\StudioProjects\mytv-android\mobile\src\main\AndroidManifest.xml:18:27-74
35            </intent-filter>
36        </activity>
37
38        <provider
38-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b2f7cfde7d9ac015d4e9e9435d414711\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
39            android:name="androidx.startup.InitializationProvider"
39-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b2f7cfde7d9ac015d4e9e9435d414711\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
40            android:authorities="top.yogiczy.mytv.mobile.androidx-startup"
40-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b2f7cfde7d9ac015d4e9e9435d414711\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
41            android:exported="false" >
41-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b2f7cfde7d9ac015d4e9e9435d414711\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
42            <meta-data
42-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b2f7cfde7d9ac015d4e9e9435d414711\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
43                android:name="androidx.emoji2.text.EmojiCompatInitializer"
43-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b2f7cfde7d9ac015d4e9e9435d414711\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
44                android:value="androidx.startup" />
44-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\b2f7cfde7d9ac015d4e9e9435d414711\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
45            <meta-data
45-->[androidx.lifecycle:lifecycle-process:2.8.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\61721f812794f0ea57a57c17785d3d31\transformed\lifecycle-process-2.8.6\AndroidManifest.xml:29:13-31:52
46                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
46-->[androidx.lifecycle:lifecycle-process:2.8.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\61721f812794f0ea57a57c17785d3d31\transformed\lifecycle-process-2.8.6\AndroidManifest.xml:30:17-78
47                android:value="androidx.startup" />
47-->[androidx.lifecycle:lifecycle-process:2.8.6] C:\Users\<USER>\.gradle\caches\8.9\transforms\61721f812794f0ea57a57c17785d3d31\transformed\lifecycle-process-2.8.6\AndroidManifest.xml:31:17-49
48            <meta-data
48-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\dd2b759f50853a8ca4191e98976c2803\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
49                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
49-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\dd2b759f50853a8ca4191e98976c2803\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
50                android:value="androidx.startup" />
50-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\dd2b759f50853a8ca4191e98976c2803\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
51        </provider>
52
53        <activity
53-->[androidx.compose.ui:ui-test-manifest:1.7.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\0bdb13050b39e5ec5f87e73f51b7ba0d\transformed\ui-test-manifest-1.7.4\AndroidManifest.xml:23:9-25:39
54            android:name="androidx.activity.ComponentActivity"
54-->[androidx.compose.ui:ui-test-manifest:1.7.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\0bdb13050b39e5ec5f87e73f51b7ba0d\transformed\ui-test-manifest-1.7.4\AndroidManifest.xml:24:13-63
55            android:exported="true" />
55-->[androidx.compose.ui:ui-test-manifest:1.7.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\0bdb13050b39e5ec5f87e73f51b7ba0d\transformed\ui-test-manifest-1.7.4\AndroidManifest.xml:25:13-36
56        <activity
56-->[androidx.compose.ui:ui-tooling-android:1.7.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\1020d7831843ef581ff10fd389e83ec5\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
57            android:name="androidx.compose.ui.tooling.PreviewActivity"
57-->[androidx.compose.ui:ui-tooling-android:1.7.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\1020d7831843ef581ff10fd389e83ec5\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
58            android:exported="true" />
58-->[androidx.compose.ui:ui-tooling-android:1.7.4] C:\Users\<USER>\.gradle\caches\8.9\transforms\1020d7831843ef581ff10fd389e83ec5\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
59
60        <receiver
60-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\dd2b759f50853a8ca4191e98976c2803\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
61            android:name="androidx.profileinstaller.ProfileInstallReceiver"
61-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\dd2b759f50853a8ca4191e98976c2803\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
62            android:directBootAware="false"
62-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\dd2b759f50853a8ca4191e98976c2803\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
63            android:enabled="true"
63-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\dd2b759f50853a8ca4191e98976c2803\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
64            android:exported="true"
64-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\dd2b759f50853a8ca4191e98976c2803\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
65            android:permission="android.permission.DUMP" >
65-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\dd2b759f50853a8ca4191e98976c2803\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
66            <intent-filter>
66-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\dd2b759f50853a8ca4191e98976c2803\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
67                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
67-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\dd2b759f50853a8ca4191e98976c2803\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
67-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\dd2b759f50853a8ca4191e98976c2803\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
68            </intent-filter>
69            <intent-filter>
69-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\dd2b759f50853a8ca4191e98976c2803\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
70                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
70-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\dd2b759f50853a8ca4191e98976c2803\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
70-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\dd2b759f50853a8ca4191e98976c2803\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
71            </intent-filter>
72            <intent-filter>
72-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\dd2b759f50853a8ca4191e98976c2803\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
73                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
73-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\dd2b759f50853a8ca4191e98976c2803\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
73-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\dd2b759f50853a8ca4191e98976c2803\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
74            </intent-filter>
75            <intent-filter>
75-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\dd2b759f50853a8ca4191e98976c2803\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
76                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
76-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\dd2b759f50853a8ca4191e98976c2803\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
76-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\dd2b759f50853a8ca4191e98976c2803\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
77            </intent-filter>
78        </receiver>
79    </application>
80
81</manifest>
