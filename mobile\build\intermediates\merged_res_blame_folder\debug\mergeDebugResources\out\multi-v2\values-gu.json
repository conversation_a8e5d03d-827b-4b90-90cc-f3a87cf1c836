{"logs": [{"outputFile": "top.yogiczy.mytv.mobile-mergeDebugResources-60:/values-gu/values-gu.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\f0ce41bc1ab5a7ccfdd795c249b2c5b2\\transformed\\ui-release\\res\\values-gu\\values-gu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,197,279,372,471,558,644,745,832,918,1001,1084,1159,1234,1309,1384,1460,1526", "endColumns": "91,81,92,98,86,85,100,86,85,82,82,74,74,74,74,75,65,115", "endOffsets": "192,274,367,466,553,639,740,827,913,996,1079,1154,1229,1304,1379,1455,1521,1637"}, "to": {"startLines": "36,37,38,39,40,41,42,100,101,102,103,105,106,107,108,110,111,112", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3506,3598,3680,3773,3872,3959,4045,10384,10471,10557,10640,10804,10879,10954,11029,11205,11281,11347", "endColumns": "91,81,92,98,86,85,100,86,85,82,82,74,74,74,74,75,65,115", "endOffsets": "3593,3675,3768,3867,3954,4040,4141,10466,10552,10635,10718,10874,10949,11024,11099,11276,11342,11458"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\136feb759fbdaa722f3905caf7f29ff7\\transformed\\material3-release\\res\\values-gu\\values-gu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,169,281,406,518,613,712,828,967,1087,1229,1314,1418,1512,1612,1726,1854,1963,2098,2230,2360,2539,2665,2787,2913,3048,3143,3239,3366,3496,3597,3702,3809,3944,4085,4194,4296,4371,4468,4564,4671,4756,4843,4941,5021,5105,5205,5308,5406,5506,5593,5699,5798,5901,6019,6099,6199", "endColumns": "113,111,124,111,94,98,115,138,119,141,84,103,93,99,113,127,108,134,131,129,178,125,121,125,134,94,95,126,129,100,104,106,134,140,108,101,74,96,95,106,84,86,97,79,83,99,102,97,99,86,105,98,102,117,79,99,93", "endOffsets": "164,276,401,513,608,707,823,962,1082,1224,1309,1413,1507,1607,1721,1849,1958,2093,2225,2355,2534,2660,2782,2908,3043,3138,3234,3361,3491,3592,3697,3804,3939,4080,4189,4291,4366,4463,4559,4666,4751,4838,4936,5016,5100,5200,5303,5401,5501,5588,5694,5793,5896,6014,6094,6194,6288"}, "to": {"startLines": "43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4146,4260,4372,4497,4609,4704,4803,4919,5058,5178,5320,5405,5509,5603,5703,5817,5945,6054,6189,6321,6451,6630,6756,6878,7004,7139,7234,7330,7457,7587,7688,7793,7900,8035,8176,8285,8387,8462,8559,8655,8762,8847,8934,9032,9112,9196,9296,9399,9497,9597,9684,9790,9889,9992,10110,10190,10290", "endColumns": "113,111,124,111,94,98,115,138,119,141,84,103,93,99,113,127,108,134,131,129,178,125,121,125,134,94,95,126,129,100,104,106,134,140,108,101,74,96,95,106,84,86,97,79,83,99,102,97,99,86,105,98,102,117,79,99,93", "endOffsets": "4255,4367,4492,4604,4699,4798,4914,5053,5173,5315,5400,5504,5598,5698,5812,5940,6049,6184,6316,6446,6625,6751,6873,6999,7134,7229,7325,7452,7582,7683,7788,7895,8030,8171,8280,8382,8457,8554,8650,8757,8842,8929,9027,9107,9191,9291,9394,9492,9592,9679,9785,9884,9987,10105,10185,10285,10379"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\55d105b608835fb0a5975933fd0070b6\\transformed\\core-1.13.1\\res\\values-gu\\values-gu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,149,252,349,451,553,651,773", "endColumns": "93,102,96,101,101,97,121,100", "endOffsets": "144,247,344,446,548,646,768,869"}, "to": {"startLines": "29,30,31,32,33,34,35,109", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2788,2882,2985,3082,3184,3286,3384,11104", "endColumns": "93,102,96,101,101,97,121,100", "endOffsets": "2877,2980,3077,3179,3281,3379,3501,11200"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\fd2de9df1fcb7d47d0f664953b17c729\\transformed\\foundation-release\\res\\values-gu\\values-gu.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,140", "endColumns": "84,84", "endOffsets": "135,220"}, "to": {"startLines": "113,114", "startColumns": "4,4", "startOffsets": "11463,11548", "endColumns": "84,84", "endOffsets": "11543,11628"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\c98e1a41ddbd0f58bbf4b7505ad9616c\\transformed\\appcompat-1.7.0\\res\\values-gu\\values-gu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,316,423,510,610,730,808,885,976,1069,1164,1258,1358,1451,1546,1640,1731,1822,1902,2008,2109,2206,2315,2415,2525,2685,2788", "endColumns": "106,103,106,86,99,119,77,76,90,92,94,93,99,92,94,93,90,90,79,105,100,96,108,99,109,159,102,80", "endOffsets": "207,311,418,505,605,725,803,880,971,1064,1159,1253,1353,1446,1541,1635,1726,1817,1897,2003,2104,2201,2310,2410,2520,2680,2783,2864"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,104", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,316,423,510,610,730,808,885,976,1069,1164,1258,1358,1451,1546,1640,1731,1822,1902,2008,2109,2206,2315,2415,2525,2685,10723", "endColumns": "106,103,106,86,99,119,77,76,90,92,94,93,99,92,94,93,90,90,79,105,100,96,108,99,109,159,102,80", "endOffsets": "207,311,418,505,605,725,803,880,971,1064,1159,1253,1353,1446,1541,1635,1726,1817,1897,2003,2104,2201,2310,2410,2520,2680,2783,10799"}}]}]}